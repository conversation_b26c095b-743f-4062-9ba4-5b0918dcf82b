package com.jlr.ecp.subscription.api.temp;

import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.temp.dto.ImportProcessResultDTO;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.temp.TempConsumerVehicleImportDO;
import com.jlr.ecp.subscription.dal.repository.IncontrolVehicleRepository;
import com.jlr.ecp.subscription.dal.repository.temp.TempConsumerVehicleImportRepository;
import com.jlr.ecp.subscription.enums.consumer.ConsumerBindEnum;
import com.jlr.ecp.subscription.enums.consumer.ConsumerVehicleSourceEnum;
import com.jlr.ecp.subscription.service.vehicle.VinInitializeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@Validated
@Slf4j
public class VinBindTempImportApiImpl implements VinBindTempImportApi {

    @Resource
    private TempConsumerVehicleImportRepository consumerVehicleImportRepository;

    @Resource
    private IncontrolVehicleRepository incontrolVehicleRepository;

    @Resource
    private VinInitializeService vinInitializeService;

    private static final Integer VEHICLE_BATCH_SIZE = 5000;

    /**
     * 根据状态获取有车辆数据的导入记录ID列表
     *
     * @param status 状态值，用于筛选符合条件的车辆导入记录
     * @return 返回包含车辆导入记录ID的列表结果对象
     */
    @Override
    public CommonResult<List<Long>> getIdListByStatusInVehicle(Integer status) {
        log.info("根据状态获取有车辆数据的导入记录ID列表, status:{}", status);
        List<TempConsumerVehicleImportDO> importDOList = consumerVehicleImportRepository.getImportListByStatus(status);
        log.info("根据状态获取有车辆数据的导入记录ID列表, importDOList数量:{}", CollUtil.size(importDOList));
        Set<String> vinSet = importDOList.stream().map(TempConsumerVehicleImportDO::getVin).collect(Collectors.toSet());
        List<List<String>> vinSetGroup = CollUtil.split(vinSet, VEHICLE_BATCH_SIZE);
        log.info("根据状态获取有车辆数据的导入记录ID列表, 每批数量:{}, vinSetGroup数量:{}", VEHICLE_BATCH_SIZE, CollUtil.size(vinSetGroup));
        Map<String, IncontrolVehicleDO> allVehicleDOMap = new HashMap<>();
        for (List<String> vinBatch : vinSetGroup) {
            Map<String, IncontrolVehicleDO> vehicleDOMap = incontrolVehicleRepository.queryIncontrolVehicleDOMap(vinBatch);
            allVehicleDOMap.putAll(vehicleDOMap);
        }
        log.info("根据状态获取有车辆数据的导入记录ID列表, allVehicleDOMap数量:{}", CollUtil.size(allVehicleDOMap));
        List<Long> idList = getIdListInVehicle(importDOList, allVehicleDOMap);
        log.info("根据状态获取有车辆数据的导入记录ID列表, idList数量:{}", CollUtil.size(idList));
        return CommonResult.success(idList);
    }

    /**
     * 根据状态获取非车辆记录的ID列表
     *
     * @param status 状态值，用于筛选记录
     * @return 返回指定状态的非车辆记录ID列表的通用结果对象
     */
    @Override
    public CommonResult<List<Long>> getIdListByStatusNonVehicle(Integer status) {
        log.info("根据状态获取非车辆记录的ID列表, status:{}", status);
        List<TempConsumerVehicleImportDO> importDOList = consumerVehicleImportRepository.getImportListByStatus(status);
        log.info("根据状态获取非车辆记录的ID列表, importDOList数量:{}", CollUtil.size(importDOList));
        Set<String> vinSet = importDOList.stream().map(TempConsumerVehicleImportDO::getVin).collect(Collectors.toSet());
        List<List<String>> vinSetGroup = CollUtil.split(vinSet, VEHICLE_BATCH_SIZE);
        log.info("根据状态获取非车辆记录的ID列表, 每批数量:{}, vinSetGroup数量:{}", VEHICLE_BATCH_SIZE, CollUtil.size(vinSetGroup));
        Map<String, IncontrolVehicleDO> allVehicleDOMap = new HashMap<>();
        for (List<String> vinBatch : vinSetGroup) {
            Map<String, IncontrolVehicleDO> vehicleDOMap = incontrolVehicleRepository.queryIncontrolVehicleDOMap(vinBatch);
            allVehicleDOMap.putAll(vehicleDOMap);
        }
        log.info("根据状态获取非车辆记录的ID列表, allVehicleDOMap数量:{}", CollUtil.size(allVehicleDOMap));
        List<Long> idList = getIdListNonVehicle(importDOList, allVehicleDOMap);
        log.info("根据状态获取非车辆记录的ID列表, idList数量:{}", CollUtil.size(idList));
        return CommonResult.success(idList);
    }

    /**
     * 从导入的车辆数据列表中提取存在于车辆映射中的车辆ID列表
     *
     * @param importDOList 导入的临时消费者车辆数据列表
     * @param vehicleDOMap 车辆数据映射，键为车辆标识，值为车辆对象
     * @return 存在于车辆映射中的车辆ID列表
     */
    public List<Long> getIdListInVehicle(List<TempConsumerVehicleImportDO> importDOList,
                                         Map<String, IncontrolVehicleDO> vehicleDOMap) {
        log.info("从导入的车辆数据列表中提取存在于车辆映射中的车辆ID列表, importDOList数量:{},  vehicleDOMap数量:{}",
                CollUtil.size(importDOList), CollUtil.size(vehicleDOMap));
        List<Long> resp = new ArrayList<>();
        if (CollUtil.isEmpty(vehicleDOMap)) {
            return resp;
        }
        for (TempConsumerVehicleImportDO importDO : importDOList) {
            if (vehicleDOMap.containsKey(importDO.getVin())) {
                resp.add(importDO.getId());
            }
        }
        return resp;
    }

    /**
     * 获取非车辆记录的ID列表
     *
     * @param importDOList 导入的消费者车辆信息列表
     * @param vehicleDOMap 车辆信息映射表，key为VIN码，value为车辆信息对象
     * @return 返回在vehicleDOMap中不存在对应VIN码的导入记录的ID列表
     */
    public List<Long> getIdListNonVehicle(List<TempConsumerVehicleImportDO> importDOList,
                                         Map<String, IncontrolVehicleDO> vehicleDOMap) {
        log.info("获取非车辆记录的ID列表, importDOList数量:{},  vehicleDOMap数量:{}",
                CollUtil.size(importDOList), CollUtil.size(vehicleDOMap));
        List<Long> resp = new ArrayList<>();
        if (CollUtil.isEmpty(vehicleDOMap)) {
            return importDOList.stream().map(TempConsumerVehicleImportDO::getId).collect(Collectors.toList());
        }
        for (TempConsumerVehicleImportDO importDO : importDOList) {
            if (!vehicleDOMap.containsKey(importDO.getVin())) {
                resp.add(importDO.getId());
            }
        }
        return resp;
    }

    public Map<String, TempConsumerVehicleImportDO> getImportDOMap(List<TempConsumerVehicleImportDO> importDOList) {
        if (CollUtil.isEmpty(importDOList)) {
            return new HashMap<>();
        }
        return importDOList.stream()
                .filter(importDO -> importDO.getVin() != null)
                .collect(Collectors.toMap(TempConsumerVehicleImportDO::getVin, Function.identity(),
                        (v1,v2)->v1));
    }

    @Override
    public CommonResult<List<ImportProcessResultDTO>> processInVehicle(List<Long> idList) {
        log.info("处理vin在车辆表中的数据, idList的size: {}", idList.size());
        List<TempConsumerVehicleImportDO> importDOList = consumerVehicleImportRepository.selectByIdList(idList);
        if (CollUtil.isEmpty(importDOList)) {
            return CommonResult.error(1234567, "根据ID列表查询临时消费者车辆导入数据全为空");
        }
        log.info("处理vin在车辆表中的数据, 查询结果数量: {}", importDOList.size());
        //1.处理customer-vehicle
        batchProcessCustomerVehicle(importDOList);
        //2.单个处理service, 需要知道每个vin的服务是否处理成功
        List<ImportProcessResultDTO> resp = new ArrayList<>();
        for (TempConsumerVehicleImportDO importDO : importDOList) {
            ImportProcessResultDTO importProcessResultDTO = new ImportProcessResultDTO();
            importProcessResultDTO.setId(importDO.getId());
            Boolean result = vinInitializeService.saveOrUpdateOnlineServiceByVin(importDO.getVin());
            if (Boolean.TRUE.equals(result)) {
                importProcessResultDTO.setStatus(result);
            } else {
                importProcessResultDTO.setStatus(false);
                importProcessResultDTO.setFailReason("saveOrUpdateOnlineServiceByVin失败");
            }
            resp.add(importProcessResultDTO);
        }
        return CommonResult.success(resp);
    }

    public void batchProcessCustomerVehicle(List<TempConsumerVehicleImportDO> importDOList) {
        String vin = vinBindNotifyDto.getVin();
        String jlrId = vinBindNotifyDto.getJlrId();
        // 查询消费者信息
        ConsumerVehicleDO consumerVehicleDO = consumerVehicleRepository.getOneByCarVinAndJlrId(vin, jlrId);


        if (Objects.isNull(consumerVehicleDO.getId())) {
            consumerVehicleRepository.save(consumerVehicleDO);
        } else {
            consumerVehicleRepository.updateById(consumerVehicleDO);
        }

    }

    public void processCustomerVehicle(TempConsumerVehicleImportDO importDO, ConsumerVehicleDO consumerVehicleDO) {
        if (Objects.isNull(consumerVehicleDO)) {
            consumerVehicleDO = new ConsumerVehicleDO();
            consumerVehicleDO.setCarVin(vin);
            consumerVehicleDO.setConsumerCode(jlrId);
            consumerVehicleDO.setBindNo(vinBindNotifyDto.getBindNo());
            consumerVehicleDO.setSource(ConsumerVehicleSourceEnum.MINI_HOME.getCode());
        }

        String operateType = importDO.getOperateType();
        if (ConsumerBindEnum.BIND.getOperateType().equals(operateType)) {
            consumerVehicleDO.setBindTime(vinBindNotifyDto.getOperateTime());
            consumerVehicleDO.setBindStatus(ConsumerBindEnum.BIND.getBindStatus());
        } else if (ConsumerBindEnum.UNBIND.getOperateType().equals(operateType)) {
            consumerVehicleDO.setUnbindTime(vinBindNotifyDto.getOperateTime());
            consumerVehicleDO.setBindStatus(ConsumerBindEnum.UNBIND.getBindStatus());
        }
    }

}
