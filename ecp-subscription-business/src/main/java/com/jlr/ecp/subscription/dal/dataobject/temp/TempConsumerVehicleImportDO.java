package com.jlr.ecp.subscription.dal.dataobject.temp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import java.time.LocalDateTime;


@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class TempConsumerVehicleImportDO extends BaseDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("bind_serial_no")
    private String bindSerialNo;

    @TableField("jlr_id")
    private String jlrId;

    @TableField("vin")
    private String vin;

    @TableField("bind_time")
    private LocalDateTime bindTime;

    @TableField("operate_type")
    private String operateType;

    @TableField("invoice_date")
    private LocalDateTime invoiceDate;

    @TableField("dms_invoice_date")
    private LocalDateTime dmsInvoiceDate;

    @TableField("import_status")
    private Integer importStatus;

    @TableField("error_message")
    private String errorMessage;

    @TableField("tenant_id")
    private Integer tenantId;
}
