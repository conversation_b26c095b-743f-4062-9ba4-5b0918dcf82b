package com.jlr.ecp.subscription.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerVehicleDO;

import java.util.List;

/**
 * ConsumerVehicle Repository接口
 *
 */
public interface ConsumerVehicleRepository extends IService<ConsumerVehicleDO> {

    ConsumerVehicleDO getOneByCarVinAndJlrId(String carVin, String jlrId);

    List<ConsumerVehicleDO> getListByJlrId(String jlrId);
}
