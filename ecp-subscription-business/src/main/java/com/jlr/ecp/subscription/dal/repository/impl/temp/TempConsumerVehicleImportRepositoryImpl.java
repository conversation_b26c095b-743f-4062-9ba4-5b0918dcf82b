package com.jlr.ecp.subscription.dal.repository.impl.temp;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.temp.TempConsumerVehicleImportDO;
import com.jlr.ecp.subscription.dal.mysql.temp.TempConsumerVehicleImportDOMapper;
import com.jlr.ecp.subscription.dal.repository.temp.TempConsumerVehicleImportRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class TempConsumerVehicleImportRepositoryImpl
        extends ServiceImpl<TempConsumerVehicleImportDOMapper, TempConsumerVehicleImportDO>
        implements TempConsumerVehicleImportRepository {

    /**
     * 根据状态获取vin绑定临时表的ID列表
     *
     * @param status 状态值，如果为null则返回空列表
     * @return 符合条件的记录ID列表
     */
    @Override
    public List<TempConsumerVehicleImportDO> getImportListByStatus(Integer status) {
        if (Objects.isNull(status)) {
            log.info("根据状态获取vin绑定临时表的ID列表, status为空");
            return new ArrayList<>();
        }
        LambdaQueryWrapperX<TempConsumerVehicleImportDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(TempConsumerVehicleImportDO::getIsDeleted, false)
                .eq(TempConsumerVehicleImportDO::getImportStatus, status)
                .select(TempConsumerVehicleImportDO::getId);
        List<TempConsumerVehicleImportDO> importDOList = list(queryWrapper);
        if (CollUtil.isEmpty(importDOList)) {
            log.info("根据状态获取vin绑定临时表的ID列表, 查询结果为空, status: {}", status);
            return new ArrayList<>();
        }
        log.info("根据状态获取vin绑定临时表的ID列表, status:{}, 数量: {}", status, CollUtil.size(importDOList));
        return importDOList;
    }

    /**
     * 根据ID列表查询临时消费者车辆导入数据
     *
     * @param idList ID列表
     * @return 临时消费者车辆导入数据列表，如果查询结果为空则返回空列表
     */
    @Override
    public List<TempConsumerVehicleImportDO> selectByIdList(List<Long> idList) {
        log.info("根据ID列表查询临时消费者车辆导入数据, idList数量: {}", CollUtil.size(idList));
        if (CollUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }
        List<TempConsumerVehicleImportDO> importDOList = listByIds(idList);
        if (CollUtil.isEmpty(importDOList)) {
            log.info("根据ID列表查询临时消费者车辆导入数据, 查询结果为空, idList: {}", idList);
            return new ArrayList<>();
        }
        return importDOList;
    }
}
