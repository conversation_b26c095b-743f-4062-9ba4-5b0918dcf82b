<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jlr.ecp</groupId>
        <artifactId>ecp-subscription-service</artifactId>
        <version>1.0.98-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.jlr.ecp</groupId>
    <artifactId>ecp-subscription-business</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
            服务订阅模块，用于Remote Service的服务订阅管理
    </description>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>

        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-common</artifactId>
        </dependency>
        <!-- DB 相关 -->
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-mybatis</artifactId>

            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>

        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-rpc</artifactId>
        </dependency>


        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-job</artifactId>

        </dependency>

        <!-- 消息队列相关 -->
<!--        <dependency>-->
<!--            <groupId>com.jlr.ecp</groupId>-->
<!--            <artifactId>ecp-framework-starter-mq</artifactId>-->
<!--        </dependency>-->

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-captcha</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-social</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-monitor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-file</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-ip</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>7.3</version>
        </dependency>
        <!--<dependency>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-starter-security</artifactId>-->
        <!--</dependency>-->
        <!-- 数据权限 -->
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-biz-data-permission</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-biz-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-biz-operatelog</artifactId>
        </dependency>

        <!--OSS集成 -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.10.1</version>
        </dependency>

        <!--DP集成 -->
        <dependency>
            <groupId>com.alibaba.dt</groupId>
            <artifactId>dataphin-sdk-core-java</artifactId>
            <version>v2.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId> <!-- or any other SLF4J binding -->
                </exclusion>
            </exclusions>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.alibaba.dt</groupId>-->
<!--            <artifactId>dataphin-sdk-core-java-dependencies</artifactId>-->
<!--            <version>v2.1.0-SNAPSHOT</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                    <artifactId>slf4j-api</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.alibaba.dt</groupId>-->
<!--            <artifactId>fastjson-1.2.80_noneautotype</artifactId>-->
<!--            <version>v2.1.0-SNAPSHOT</version>-->
<!--        </dependency>-->

        <!-- SMS SDK begin -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-sms</artifactId>
        </dependency>


        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
        </dependency>
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-server</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId> <!-- 接口文档 -->
            <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>  <!-- 接口文档 -->
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>


        <dependency>
            <groupId>cn.smallbun.screw</groupId>
            <artifactId>screw-core</artifactId> <!-- 实现数据库文档 -->
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
        </dependency>
        <!--<dependency>-->
        <!--<groupId>com.esotericsoftware</groupId>-->
        <!--<artifactId>kryo</artifactId>-->
        <!--<version>5.0.0</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework.kafka</groupId>-->
            <!--<artifactId>spring-kafka</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-subscription-feign-client-sdk</artifactId>
            <version>1.0.98-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-system-feign-client-sdk</artifactId>
            <version>1.0.27-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-consumer-feign-client-sdk</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-order-feign-client-sdk</artifactId>
            <version>1.0.39-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-notification-feign-client-sdk</artifactId>
            <version>1.0.16-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-report-feign-client-sdk</artifactId>
            <version>1.0.30-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-biz-forgeRock</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.alibaba.dt</groupId>-->
<!--            <artifactId>dataphin-sdk-core-java</artifactId>-->
<!--            <version>v2.1.0-SNAPSHOT</version>-->
<!--        </dependency>-->

        <!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.10.0</version>
        </dependency>

        <!--        <dependency>-->
<!--            <groupId>com.alibaba.dt</groupId>-->
<!--            <artifactId>dataphin-sdk-core-java-dependencies</artifactId>-->
<!--            <version>v2.1.0-SNAPSHOT</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                    <artifactId>slf4j-api</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                    <artifactId>slf4j-log4j12</artifactId> &lt;!&ndash; or any other SLF4J binding &ndash;&gt;-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.apache.logging.log4j</groupId>-->
<!--                    <artifactId>log4j-slf4j-impl</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.apache.logging.log4j</groupId>-->
<!--                    <artifactId>log4j-api</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                    <artifactId>jcl-over-slf4j</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>commons-logging</groupId>-->
<!--                    <artifactId>commons-logging</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
            <version>1.15.0</version>
        </dependency>


        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-dateutil</artifactId>
            <version>3.0.7</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.dt</groupId>
            <artifactId>fastjson-1.2.80_noneautotype</artifactId>
            <version>v2.1.0</version>
        </dependency>

        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.5.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
            <version>2.15.0</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http2</artifactId>
            <version>4.1.110.Final</version>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sns</artifactId>
            <version>2.15.0</version>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sqs</artifactId>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <!--<finalName>${project.artifactId}</finalName>-->
        <finalName>app</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <configuration>
                    <excludes>
                        <exclude>**/test/**/*</exclude>
                    </excludes>
                    <includes>
                        <include>com/jlr/ecp/subscription/service/**/*</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <id>jacoco-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <phase>test-compile</phase>
                    </execution>

                    <execution>
                        <id>jacoco-site</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19</version>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <encoding>utf-8</encoding>
                    <!-- 解决Spring boot引起的profile失效问题 -->
                    <useDefaultDelimiters>true</useDefaultDelimiters>
                    <!-- 过滤后缀为p12、pem、pfx的证书文件 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                        <nonFilteredFileExtension>cer</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pem</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pfx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jkx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>jlr-ecp-nexus</id>
            <name>jlr-ecp-nexus</name>
            <url>https://nexus.jaguarlandrover.cn/repository/ecp-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>jlr-ecp-release</id>
            <name>Releases</name>
            <url>https://nexus.jaguarlandrover.cn/repository/ecp-releases/</url>
        </repository>
        <snapshotRepository>
            <id>jlr-ecp-snapshot</id>
            <name>Snapshot</name>
            <url>https://nexus.jaguarlandrover.cn/repository/ecp-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
