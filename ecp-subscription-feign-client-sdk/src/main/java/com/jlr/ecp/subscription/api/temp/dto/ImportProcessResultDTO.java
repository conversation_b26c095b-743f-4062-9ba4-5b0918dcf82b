package com.jlr.ecp.subscription.api.temp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "存量vin导入结果DTO")
public class ImportProcessResultDTO {
    @Schema(description = "存入临时表id")
    private Long id;

    @Schema(description = "处理状态")
    private Boolean status;

    @Schema(description = "失败原因")
    private String failReason;
}
